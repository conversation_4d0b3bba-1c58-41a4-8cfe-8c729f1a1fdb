package listener

import (
	"fmt"
	"log"
	"net"

	"github.com/nickabs/http/internal/request"
)

const ServerAddr = "localhost:42069"

// create a tcp listener
// accept connections and print receieved data one line at a time
func Listen() {
	listener, err := net.Listen("tcp", ServerAddr)
	if err != nil {
		panic(fmt.Sprintf("could not create listener %v", err))
	}
	fmt.Printf("listening on %s \n", ServerAddr)

	defer listener.Close()

	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Fatalf("could not get a connection %v", err)
			return
		}
		fmt.Printf("connection accepted from %s \n", conn.RemoteAddr().String())
		defer conn.Close()

	
		req := request.Request{}

		req.Parse(conn)

		fmt.Println("Request:")
		fmt.Printf("Method : %v \n", req.RequestLine.Method)
		fmt.Printf("Target : %v \n", req.RequestLine.RequestTarget)
		fmt.Printf("Version : %v \n", req.RequestLine.HttpVersion)}

}
