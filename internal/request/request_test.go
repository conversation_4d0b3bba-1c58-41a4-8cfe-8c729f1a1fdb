package request

import (
	"io"
	"strings"
	"testing"

	"github.com/nickabs/http/internal/listener"
)

func TestRequest(t *testing.T) {

	go listener.Listen()

	tests := []struct {
		name            string
		request         string
		serverAddr      string
		wantBody        string
		wantRequestLine RequestLine
		wantHeaders     int
		expectError     bool
	}{

		{
			name:    "valid server",
			request: "POST /coffee HTTP/1.1\r\n",
			wantRequestLine: RequestLine{
				HttpVersion:   "HTTP/1.1",
				RequestTarget: "/coffee",
				Method:        "POST",
			},
			wantHeaders: 0,
			serverAddr:  listener.ServerAddr,
			expectError: false,
		},
		{
			name:    "valid server w. headers",
			request: "POST /coffee HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n",
			wantRequestLine: RequestLine{
				HttpVersion:   "HTTP/1.1",
				RequestTarget: "/coffee",
				Method:        "POST",
			},
			wantHeaders: 3,
			serverAddr:  listener.ServerAddr,
			expectError: false,
		},
		{
			name:     "valid server w. headers and body",
			request:  "POST /coffee HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n{}",
			wantBody: "{}",
			wantRequestLine: RequestLine{
				HttpVersion:   "HTTP/1.1",
				RequestTarget: "/coffee",
				Method:        "POST",
			},
			wantHeaders: 3,
			serverAddr:  listener.ServerAddr,
			expectError: false,
		},
		{
			name:        "bad method",
			request:     "123 /coffee HTTP/1.1\r\n",
			expectError: true,
		},
		{
			name:        "missing method",
			request:     "/coffee HTTP/1.1\r\n",
			expectError: true,
		},
		{
			name:        "bad version ",
			request:     "POST /coffee HTTP/1.2\r\n",
			expectError: true,
		},
		{
			name:        "out of order ",
			request:     "/coffee POST HTTP/1.2\r\n",
			expectError: true,
		},
		{
			name:        "bad name",
			request:     "POST /coffee blah/1.2\r\n",
			expectError: true,
		},
		{
			name:        "empty request",
			request:     "",
			serverAddr:  listener.ServerAddr,
			expectError: true,
		},
		{
			name:        "no target response",
			request:     "POST  HTTP/1.1\r\n",
			serverAddr:  listener.ServerAddr,
			expectError: true,
		},
		{
			name:        "no crlf termination",
			request:     "POST /coffee HTTP/1.1",
			expectError: true,
		},
	}

	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {

			r := Request{}
			testReader := strings.NewReader((tt.request))

			err := r.Parse(testReader)
			if tt.expectError && err != nil {
				t.Log(err)
				return
			}

			if tt.expectError && err == nil {
				t.Errorf("expected an error but got success")
				return
			}

			if !tt.expectError && err != nil {
				t.Errorf("did not expect error but got %v", err)
				return
			}

			if len(r.Headers) != tt.wantHeaders {
				t.Errorf("wanted %v headers, got %v\n", tt.wantHeaders, len(r.Headers))
			}

			if r.RequestLine.HttpVersion != tt.wantRequestLine.HttpVersion {
				t.Errorf("expected httpVersion %v got %v ", tt.wantRequestLine.HttpVersion, r.RequestLine.HttpVersion)
			}
			if r.RequestLine.RequestTarget != tt.wantRequestLine.RequestTarget {
				t.Errorf("expected requestTarget %v got %v ", tt.wantRequestLine.RequestTarget, r.RequestLine.RequestTarget)
			}
			if r.RequestLine.Method != tt.wantRequestLine.Method {
				t.Errorf("expected requestTarget %v got %v ", tt.wantRequestLine.Method, r.RequestLine.Method)
			}

			if tt.wantBody != "" && r.Body != tt.wantBody {
				t.Errorf("expected %v body and got %v", tt.wantBody, r.Body)
			}

			/* todo
			_, err = SendRequest(tt.request, tt.serverAddr)

			if !tt.expectError && err != nil {
				t.Errorf("did not expect error but got %v", err)
				return
			}

			if tt.expectError && err == nil {
				t.Error("expected error but got success")
				return
			}
			*/

		})
	}
}

func TestBodyParsing(t *testing.T) {
	r := Request{}
	testReader := strings.NewReader("POST /test HTTP/1.1\r\nContent-Length: 13\r\n\r\nHello, World!")

	err := r.Parse(testReader)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	expectedBody := "Hello, World!"
	if r.Body != expectedBody {
		t.Errorf("expected body '%s', got '%s'", expectedBody, r.Body)
	}
}

type chunkReader struct {
	data            string
	numBytesPerRead int
	pos             int
}

func TestChunkReaderInput(t *testing.T) {
	t.Run("chunking test", func(t *testing.T) {

		req := Request{}

		reader := &chunkReader{
			data:            "GET / HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n",
			numBytesPerRead: 3,
			pos:             0,
		}

		for i := 1; i <= len(reader.data); i++ {

			t.Logf("attempting reads of %v bytes", i)

			reader.numBytesPerRead = i
			reader.pos = 0
			err := req.Parse(reader)
			if err != nil {
				t.Fatalf("could not create request from reader %v", err)
			}

			if req.RequestLine.Method != "GET" {
				t.Errorf("got %v expected GET", req.RequestLine.Method)
			}

			if len(req.Headers) != 3 {
				t.Errorf("expected 3 headers got %v", len(req.Headers))
			}
		}
	})
}

// Read reads up to len(p) or numBytesPerRead bytes from the string per call
// its useful for simulating reading a variable number of bytes per chunk from a network connection
func (cr *chunkReader) Read(p []byte) (n int, err error) {
	if cr.pos >= len(cr.data) {
		return 0, io.EOF
	}
	endIndex := cr.pos + cr.numBytesPerRead
	if endIndex > len(cr.data) {
		endIndex = len(cr.data)
	}
	n = copy(p, cr.data[cr.pos:endIndex])
	cr.pos += n
	if n > cr.numBytesPerRead {
		n = cr.numBytesPerRead
		cr.pos -= n - cr.numBytesPerRead
	}
	return n, nil
}
